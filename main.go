package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"regexp"
	"strings"
	"time"

	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5" // ✔

	// MTProto client
	"github.com/gotd/td/telegram"      // ✔
	"github.com/gotd/td/telegram/auth" // ✔
	"github.com/gotd/td/tg"            // ✔

	// YAML конфиг
	"gopkg.in/yaml.v3" // ✔
)

/* ---------- конфигурация ---------- */
type Config struct {
	APIID           int    `yaml:"api_id"`
	APIHash         string `yaml:"api_hash"`
	Phone           string `yaml:"phone"`
	BotToken        string `yaml:"bot_token"`
	ListenChannelID int64  `yaml:"listen_channel_id"`
	TargetChatID    int64  `yaml:"target_chat_id"`
}

func loadConfig(path string) (*Config, error) {
	raw, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}
	var c Config
	if err := yaml.Unmarshal(raw, &c); err != nil {
		return nil, err
	}
	return &c, nil
}

/* ---------- main ---------- */
func main() {
	cfg, err := loadConfig("config.yml")
	if err != nil {
		log.Fatalf("config: %v", err)
	}

	/* Bot API для отправки buy */
	bot, err := tgbotapi.NewBotAPI(cfg.BotToken)
	if err != nil {
		log.Fatalf("BotAPI: %v", err)
	}

	/* MTProto-клиент (user-бот) */
	client := telegram.NewClient(cfg.APIID, cfg.APIHash, telegram.Options{})
	ctx := context.Background()

	go func() {
		err := client.Run(ctx, func(ctx context.Context) error {
			flow := auth.NewFlow(
				auth.Constant(cfg.Phone, "", auth.CodeAuthenticatorFunc(askCode)),
				auth.SendCodeOptions{},
			)
			if err := client.Auth().IfNecessary(ctx, flow); err != nil {
				return err
			}

			upd := tg.NewUpdateDispatcher()
			upd.OnNewChannelMessage(func(ctx context.Context, _ tg.Entities, m *tg.Message) error {
				if peer, ok := m.PeerID.(*tg.PeerChannel); !ok || peer.ChannelID != cfg.ListenChannelID {
					return nil
				}
				if !strings.Contains(m.Message, "PumpFun testing phase:") {
					return nil
				}
				re := regexp.MustCompile(`[1-9A-HJ-NP-Za-km-z]{32,44}pump`)
				if contract := re.FindString(m.Message); contract != "" {
					msg := fmt.Sprintf("buy %s", contract)
					_, _ = bot.Send(tgbotapi.NewMessage(cfg.TargetChatID, msg))
					log.Printf("Buy signal sent: %s", msg)
				}
				return nil
			})

			return runUpdates(ctx, client, &upd)
		})
		if err != nil {
			log.Fatalf("TD run: %v", err)
		}
	}()

	select {}
}

/* ---------- вспомогательные функции ---------- */

func askCode(ctx context.Context, _ *tg.AuthSentCode) (string, error) {
	var code string
	fmt.Print("Enter Telegram code: ")
	_, _ = fmt.Scanln(&code)
	return code, nil
}

func runUpdates(ctx context.Context, client *telegram.Client, upd *tg.UpdateDispatcher) error {
	raw := make(chan tg.UpdateClass, 128)
	go upd.Run(ctx, raw)
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case u := <-raw:
			upd.Dispatch(ctx, u)
		case <-time.After(30 * time.Second):
			if err := client.KeepAlive(ctx); err != nil {
				log.Println("keep-alive:", err)
			}
		}
	}
}
