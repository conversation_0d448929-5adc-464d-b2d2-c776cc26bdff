package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"regexp"
	"strings"

	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5" // ✔

	// MTProto client
	"github.com/gotd/td/session"       // ✔
	"github.com/gotd/td/telegram"      // ✔
	"github.com/gotd/td/telegram/auth" // ✔
	"github.com/gotd/td/tg"            // ✔

	// YAML конфиг
	"gopkg.in/yaml.v3" // ✔
)

/* ---------- конфигурация ---------- */
type Config struct {
	APIID           int    `yaml:"api_id"`
	APIHash         string `yaml:"api_hash"`
	Phone           string `yaml:"phone"`
	BotToken        string `yaml:"bot_token"`
	ListenChannelID int64  `yaml:"listen_channel_id"`
	TargetChatID    int64  `yaml:"target_chat_id"`
	Filter          string `yaml:"filter"`          // Фильтр сообщений (если пустой - без фильтра)
	EnableLogging   bool   `yaml:"enable_logging"`  // Включить подробное логирование
}

func loadConfig(path string) (*Config, error) {
	raw, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}
	var c Config
	if err := yaml.Unmarshal(raw, &c); err != nil {
		return nil, err
	}
	return &c, nil
}

/* ---------- main ---------- */
func main() {
	cfg, err := loadConfig("config.yml")
	if err != nil {
		log.Fatalf("❌ Ошибка загрузки конфигурации: %v", err)
	}

	log.Printf("🚀 Запуск Telegram парсера...")
	log.Printf("📱 Телефон: %s", cfg.Phone)
	log.Printf("📺 Канал для прослушивания: %d", cfg.ListenChannelID)
	log.Printf("🎯 Целевой чат: %d", cfg.TargetChatID)
	if cfg.Filter != "" {
		log.Printf("🔍 Фильтр сообщений: '%s'", cfg.Filter)
	} else {
		log.Printf("🔍 Фильтр сообщений: отключен (обрабатываются все сообщения)")
	}
	log.Printf("📝 Подробное логирование: %t", cfg.EnableLogging)

	/* Bot API для отправки buy */
	bot, err := tgbotapi.NewBotAPI(cfg.BotToken)
	if err != nil {
		log.Fatalf("❌ Ошибка создания Bot API: %v", err)
	}
	log.Printf("🤖 Bot API инициализирован: @%s", bot.Self.UserName)

	/* MTProto-клиент (user-бот) */
	updateHandler := telegram.UpdateHandlerFunc(func(ctx context.Context, u tg.UpdatesClass) error {
		if cfg.EnableLogging {
			log.Printf("🔄 Получено обновление типа: %T", u)
		}

		switch upd := u.(type) {
		case *tg.Updates:
			if cfg.EnableLogging {
				log.Printf("📦 Updates содержит %d обновлений", len(upd.Updates))
			}
			for i, update := range upd.Updates {
				if cfg.EnableLogging {
					log.Printf("📝 Обновление %d/%d типа: %T", i+1, len(upd.Updates), update)
				}

				if channelMsg, ok := update.(*tg.UpdateNewChannelMessage); ok {
					if cfg.EnableLogging {
						log.Printf("📺 Найдено сообщение в канале")
					}

					if msg, ok := channelMsg.Message.(*tg.Message); ok {
						if cfg.EnableLogging {
							log.Printf("💬 Тип сообщения: %T", channelMsg.Message)
							log.Printf("📄 Текст сообщения: %s", msg.Message)
							log.Printf("🆔 PeerID: %T", msg.PeerID)
						}

						if peer, ok := msg.PeerID.(*tg.PeerChannel); ok {
							if cfg.EnableLogging {
								log.Printf("📺 ID канала в сообщении: %d", peer.ChannelID)
								log.Printf("🎯 Ожидаемый ID канала: %d", cfg.ListenChannelID)
								log.Printf("✅ Совпадение каналов: %t", peer.ChannelID == cfg.ListenChannelID)
							}

							if peer.ChannelID == cfg.ListenChannelID {
								log.Printf("📨 Получено сообщение из целевого канала %d: %s", peer.ChannelID, msg.Message)

								// Проверяем фильтр
								shouldProcess := false
								if cfg.Filter == "" {
									// Если фильтр пустой - обрабатываем все сообщения
									shouldProcess = true
									if cfg.EnableLogging {
										log.Printf("🔍 Фильтр не задан, обрабатываем все сообщения")
									}
								} else {
									// Если фильтр задан - проверяем его
									shouldProcess = strings.Contains(msg.Message, cfg.Filter)
									if cfg.EnableLogging {
										if shouldProcess {
											log.Printf("✅ Сообщение прошло фильтр '%s'", cfg.Filter)
										} else {
											log.Printf("❌ Сообщение не прошло фильтр '%s'", cfg.Filter)
										}
									}
								}

								if shouldProcess {
									// Ищем контракт в сообщении
									re := regexp.MustCompile(`[1-9A-HJ-NP-Za-km-z]{32,44}pump`)
									if contract := re.FindString(msg.Message); contract != "" {
										message := fmt.Sprintf("buy %s", contract)
										_, err := bot.Send(tgbotapi.NewMessage(cfg.TargetChatID, message))
										if err != nil {
											log.Printf("❌ Ошибка отправки сообщения: %v", err)
										} else {
											log.Printf("🚀 Buy signal sent: %s", message)
										}
									} else {
										if cfg.EnableLogging {
											log.Printf("🔍 Контракт не найден в сообщении")
										}
									}
								}
							} else {
								if cfg.EnableLogging {
									log.Printf("⏭️ Сообщение из другого канала %d, пропускаем", peer.ChannelID)
								}
							}
						} else {
							if cfg.EnableLogging {
								log.Printf("❓ PeerID не является каналом: %T", msg.PeerID)
							}
						}
					} else {
						if cfg.EnableLogging {
							log.Printf("❓ Сообщение не является обычным сообщением: %T", channelMsg.Message)
						}
					}
				} else {
					if cfg.EnableLogging {
						log.Printf("⏭️ Обновление не является сообщением канала: %T", update)
					}
				}
			}
		case *tg.UpdateShort:
			if cfg.EnableLogging {
				log.Printf("📨 Получено короткое обновление: %T", upd.Update)
			}
		case *tg.UpdateShortMessage:
			if cfg.EnableLogging {
				log.Printf("📨 Получено короткое сообщение от пользователя %d: %s", upd.UserID, upd.Message)
			}
		case *tg.UpdateShortChatMessage:
			if cfg.EnableLogging {
				log.Printf("📨 Получено короткое сообщение из чата %d от пользователя %d: %s", upd.ChatID, upd.FromID, upd.Message)
			}
		case *tg.UpdatesTooLong:
			if cfg.EnableLogging {
				log.Printf("⚠️ Слишком много обновлений, нужна синхронизация")
			}
		default:
			if cfg.EnableLogging {
				log.Printf("❓ Неизвестный тип обновления: %T", upd)
			}
		}
		return nil
	})

	// Создаем хранилище сессии
	sessionStorage := &session.FileStorage{
		Path: "session.json",
	}
	log.Printf("💾 Хранилище сессии: session.json")

	client := telegram.NewClient(cfg.APIID, cfg.APIHash, telegram.Options{
		UpdateHandler:  updateHandler,
		SessionStorage: sessionStorage,
	})
	ctx := context.Background()
	log.Printf("🔗 Telegram клиент создан")

	go func() {
		log.Printf("🔄 Запуск Telegram клиента...")
		err := client.Run(ctx, func(ctx context.Context) error {
			log.Printf("🔐 Проверка аутентификации...")
			flow := auth.NewFlow(
				auth.Constant(cfg.Phone, "", auth.CodeAuthenticatorFunc(askCode)),
				auth.SendCodeOptions{},
			)
			if err := client.Auth().IfNecessary(ctx, flow); err != nil {
				log.Printf("❌ Ошибка аутентификации: %v", err)
				return err
			}
			log.Printf("✅ Аутентификация успешна!")
			log.Printf("👂 Начинаем прослушивание обновлений...")

			// Просто ждем
			select {
			case <-ctx.Done():
				log.Printf("🛑 Получен сигнал остановки")
				return ctx.Err()
			}
		})
		if err != nil {
			log.Fatalf("❌ Ошибка работы Telegram клиента: %v", err)
		}
	}()

	log.Printf("🎯 Парсер запущен и готов к работе!")
	log.Printf("🔄 Для остановки нажмите Ctrl+C")
	select {}
}

/* ---------- вспомогательные функции ---------- */

func askCode(ctx context.Context, sentCode *tg.AuthSentCode) (string, error) {
	var code string
	log.Printf("📱 Код аутентификации отправлен на ваш телефон")
	if sentCode.Type != nil {
		switch codeType := sentCode.Type.(type) {
		case *tg.AuthSentCodeTypeSMS:
			log.Printf("📨 Код отправлен через SMS")
		case *tg.AuthSentCodeTypeCall:
			log.Printf("📞 Код будет продиктован по телефону")
		case *tg.AuthSentCodeTypeFlashCall:
			log.Printf("📞 Код в номере входящего звонка")
		case *tg.AuthSentCodeTypeApp:
			log.Printf("📱 Код отправлен через Telegram приложение")
		default:
			log.Printf("📨 Код отправлен (тип: %T)", codeType)
		}
	}
	fmt.Print("🔑 Введите код аутентификации: ")
	_, _ = fmt.Scanln(&code)
	log.Printf("✅ Код введен, проверяем...")
	return code, nil
}


