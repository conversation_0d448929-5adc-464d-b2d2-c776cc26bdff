# Конфигурация Telegram парсера
# Скопируйте этот файл в config.yml и заполните своими данными

# Telegram API данные (получить на https://my.telegram.org/apps)
api_id: 12345678
api_hash: "your_api_hash_here"

# Ваш номер телефона (с кодом страны, например +1234567890)
phone: "+1234567890"

# Токен бота для отправки сообщений (получить у @BotFather)
bot_token: "your_bot_token_here"

# ID канала для прослушивания (можно получить через @userinfobot)
listen_channel_id: -1001234567890

# ID чата куда отправлять buy сигналы
target_chat_id: -1001234567890

# Фильтр сообщений (если пустой - обрабатываются все сообщения)
# Примеры:
# filter: "PumpFun testing phase:"  # только сообщения с этим текстом
# filter: ""                       # все сообщения (без фильтра)
filter: "PumpFun testing phase:"

# Включить подробное логирование (true/false)
enable_logging: true
