# Telegram PumpFun Parser

Парсер для мониторинга каналов Telegram и автоматической отправки buy сигналов при обнаружении контрактов PumpFun.

## 🚀 Быстрый старт

1. **Скопируйте конфигурацию:**
   ```bash
   cp config.yml.example config.yml
   ```

2. **Заполните config.yml:**
   - `api_id` и `api_hash` - получите на https://my.telegram.org/apps
   - `phone` - ваш номер телефона с кодом страны
   - `bot_token` - токен бота от @BotFather
   - `listen_channel_id` - ID канала для прослушивания
   - `target_chat_id` - ID чата для отправки сигналов

3. **Запустите парсер:**
   ```bash
   go run main.go
   ```

## 🔧 Получение ID канала

Если вы не знаете ID канала, используйте утилиту:

```bash
go run get_channel_id.go @channel_username
```

Пример:
```bash
go run get_channel_id.go @pumpfun_signals
```

## 🐛 Отладка проблем

### Сообщения не обрабатываются

1. **Включите подробное логирование:**
   ```yaml
   enable_logging: true
   ```

2. **Проверьте ID канала:**
   - Используйте утилиту `get_channel_id.go`
   - Убедитесь, что ID в конфиге правильный

3. **Проверьте фильтр:**
   - Если `filter: ""` - обрабатываются ВСЕ сообщения
   - Если `filter: "текст"` - только сообщения с этим текстом

4. **Проверьте права доступа:**
   - Убедитесь, что ваш аккаунт подписан на канал
   - Проверьте, что канал публичный или вы имеете доступ

### Типичные проблемы

**Проблема:** Парсер запускается, но не видит сообщения
**Решение:** 
- Проверьте ID канала с помощью `get_channel_id.go`
- Убедитесь, что вы подписаны на канал
- Включите `enable_logging: true` для отладки

**Проблема:** Код аутентификации запрашивается каждый раз
**Решение:** 
- Проверьте, что файл `session.json` создается
- Убедитесь, что у программы есть права на запись

**Проблема:** Контракты не отправляются
**Решение:**
- Проверьте регулярное выражение для контрактов
- Убедитесь, что бот имеет права на отправку в целевой чат

## 📝 Логи

При включенном `enable_logging: true` вы увидите:

- 🔄 Все типы получаемых обновлений
- 📺 Сообщения из каналов с ID
- 🔍 Работу фильтров
- 🚀 Отправку buy сигналов
- ❌ Ошибки и проблемы

## ⚙️ Конфигурация

```yaml
# Telegram API
api_id: 12345678
api_hash: "your_hash"
phone: "+1234567890"

# Bot для отправки
bot_token: "bot_token"

# Каналы
listen_channel_id: -1001234567890  # Канал для прослушивания
target_chat_id: -1001234567890     # Чат для отправки сигналов

# Фильтрация
filter: "PumpFun testing phase:"   # Фильтр (пустой = без фильтра)
enable_logging: true               # Подробные логи
```

## 🔒 Безопасность

- Файл `session.json` содержит данные авторизации - храните его в безопасности
- Не делитесь файлом конфигурации с API ключами
- Используйте отдельного бота для отправки сообщений
