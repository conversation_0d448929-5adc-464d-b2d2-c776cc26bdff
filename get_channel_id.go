package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/gotd/td/session"
	"github.com/gotd/td/telegram"
	"github.com/gotd/td/telegram/auth"
	"github.com/gotd/td/tg"
	"gopkg.in/yaml.v3"
)

// Утилита для получения ID каналов
func main() {
	if len(os.Args) < 2 {
		fmt.Println("Использование: go run get_channel_id.go <username_канала>")
		fmt.Println("Пример: go run get_channel_id.go @channel_name")
		os.Exit(1)
	}

	channelUsername := os.Args[1]
	
	cfg, err := loadConfig("config.yml")
	if err != nil {
		log.Fatalf("❌ Ошибка загрузки конфигурации: %v", err)
	}

	sessionStorage := &session.FileStorage{
		Path: "session.json",
	}

	client := telegram.NewClient(cfg.APIID, cfg.APIHash, telegram.Options{
		SessionStorage: sessionStorage,
	})

	ctx := context.Background()

	err = client.Run(ctx, func(ctx context.Context) error {
		flow := auth.NewFlow(
			auth.Constant(cfg.Phone, "", auth.CodeAuthenticatorFunc(askCode)),
			auth.SendCodeOptions{},
		)
		if err := client.Auth().IfNecessary(ctx, flow); err != nil {
			return err
		}

		// Ищем канал
		result, err := client.API().ContactsResolveUsername(ctx, &tg.ContactsResolveUsernameRequest{
			Username: channelUsername,
		})
		if err != nil {
			return fmt.Errorf("не удалось найти канал %s: %v", channelUsername, err)
		}

		for _, chat := range result.Chats {
			if channel, ok := chat.(*tg.Channel); ok {
				fmt.Printf("🎯 Найден канал: %s\n", channel.Title)
				fmt.Printf("📺 ID канала: %d\n", channel.ID)
				fmt.Printf("👥 Участников: %d\n", channel.ParticipantsCount)
				fmt.Printf("📝 Username: @%s\n", channel.Username)
				
				// Для использования в конфиге нужно добавить префикс -100
				channelID := int64(-1000000000000) - int64(channel.ID)
				fmt.Printf("🔧 ID для config.yml: %d\n", channelID)
			}
		}

		return nil
	})

	if err != nil {
		log.Fatalf("❌ Ошибка: %v", err)
	}
}

func askCode(ctx context.Context, sentCode *tg.AuthSentCode) (string, error) {
	var code string
	fmt.Print("🔑 Введите код аутентификации: ")
	fmt.Scanln(&code)
	return code, nil
}

type Config struct {
	APIID           int    `yaml:"api_id"`
	APIHash         string `yaml:"api_hash"`
	Phone           string `yaml:"phone"`
	BotToken        string `yaml:"bot_token"`
	ListenChannelID int64  `yaml:"listen_channel_id"`
	TargetChatID    int64  `yaml:"target_chat_id"`
	Filter          string `yaml:"filter"`
	EnableLogging   bool   `yaml:"enable_logging"`
}

func loadConfig(path string) (*Config, error) {
	raw, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}
	var c Config
	if err := yaml.Unmarshal(raw, &c); err != nil {
		return nil, err
	}
	return &c, nil
}
