package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/gotd/td/session"
	"github.com/gotd/td/telegram"
	"github.com/gotd/td/telegram/auth"
	"github.com/gotd/td/tg"
	"gopkg.in/yaml.v3"
)

// Утилита для получения списка всех каналов
func main() {
	cfg, err := loadConfig("config.yml")
	if err != nil {
		log.Fatalf("❌ Ошибка загрузки конфигурации: %v", err)
	}

	sessionStorage := &session.FileStorage{
		Path: "session.json",
	}

	client := telegram.NewClient(cfg.APIID, cfg.APIHash, telegram.Options{
		SessionStorage: sessionStorage,
	})

	ctx := context.Background()

	err = client.Run(ctx, func(ctx context.Context) error {
		flow := auth.NewFlow(
			auth.Constant(cfg.Phone, "", auth.CodeAuthenticatorFunc(askCode)),
			auth.SendCodeOptions{},
		)
		if err := client.Auth().IfNecessary(ctx, flow); err != nil {
			return err
		}

		log.Printf("✅ Аутентификация успешна!")
		log.Printf("📋 Получаем список всех ваших каналов...")

		// Получаем все диалоги
		dialogs, err := client.API().MessagesGetDialogs(ctx, &tg.MessagesGetDialogsRequest{
			Limit: 100,
		})
		if err != nil {
			return fmt.Errorf("не удалось получить диалоги: %v", err)
		}

		fmt.Printf("\n🎯 НАЙДЕННЫЕ КАНАЛЫ:\n")
		fmt.Printf("═══════════════════════════════════════════════════════════════\n")

		var dialogsSlice *tg.MessagesDialogs
		switch d := dialogs.(type) {
		case *tg.MessagesDialogs:
			dialogsSlice = d
		case *tg.MessagesDialogsSlice:
			dialogsSlice = &tg.MessagesDialogs{
				Dialogs: d.Dialogs,
				Chats:   d.Chats,
				Users:   d.Users,
			}
		default:
			return fmt.Errorf("неизвестный тип диалогов: %T", dialogs)
		}

		channelCount := 0
		for _, chat := range dialogsSlice.Chats {
			if channel, ok := chat.(*tg.Channel); ok {
				channelCount++
				
				// Для использования в конфиге нужно добавить префикс -100
				channelID := int64(-1000000000000) - int64(channel.ID)
				
				fmt.Printf("📺 Канал: %s\n", channel.Title)
				if channel.Username != "" {
					fmt.Printf("   Username: @%s\n", channel.Username)
				} else {
					fmt.Printf("   Username: (приватный канал)\n")
				}
				fmt.Printf("   ID для config.yml: %d\n", channelID)
				fmt.Printf("   Участников: %d\n", channel.ParticipantsCount)
				fmt.Printf("   Тип: ")
				if channel.Broadcast {
					fmt.Printf("Канал")
				} else {
					fmt.Printf("Супергруппа")
				}
				if !channel.Username != "" {
					fmt.Printf(" (публичный)")
				} else {
					fmt.Printf(" (приватный)")
				}
				fmt.Printf("\n")
				fmt.Printf("───────────────────────────────────────────────────────────────\n")
			}
		}

		if channelCount == 0 {
			fmt.Printf("❌ Каналы не найдены\n")
		} else {
			fmt.Printf("✅ Найдено каналов: %d\n", channelCount)
		}

		return nil
	})

	if err != nil {
		log.Fatalf("❌ Ошибка: %v", err)
	}
}

func askCode(ctx context.Context, sentCode *tg.AuthSentCode) (string, error) {
	var code string
	fmt.Print("🔑 Введите код аутентификации: ")
	fmt.Scanln(&code)
	return code, nil
}

type Config struct {
	APIID           int    `yaml:"api_id"`
	APIHash         string `yaml:"api_hash"`
	Phone           string `yaml:"phone"`
	BotToken        string `yaml:"bot_token"`
	ListenChannelID int64  `yaml:"listen_channel_id"`
	TargetChatID    int64  `yaml:"target_chat_id"`
	Filter          string `yaml:"filter"`
	EnableLogging   bool   `yaml:"enable_logging"`
}

func loadConfig(path string) (*Config, error) {
	raw, err := os.ReadFile(path)
	if err != nil {
		return nil, err
	}
	var c Config
	if err := yaml.Unmarshal(raw, &c); err != nil {
		return nil, err
	}
	return &c, nil
}
